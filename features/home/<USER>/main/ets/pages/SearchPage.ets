import { buildTitleBar, CommonConstants } from 'commonlib';
import { LengthMetrics } from '@kit.ArkUI';
import { SearchPageVM } from '../viewModels/SearchPageVM';

@Builder
export function SearchPageBuilder() {
  SearchPage();
}

@Preview
@ComponentV2
struct SearchPage {
  vm: SearchPageVM = new SearchPageVM();

  aboutToAppear(): void {
    this.vm.getHotkeys();
    this.vm.getDefaultSearch();
  }

  build() {
    NavDestination() {
      Column() {
        // TODO: 替换为播放器搜索功能
        Text('搜索功能')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .margin({ top: 50, bottom: 20 })

        Text('这里将实现视频搜索功能')
          .fontSize(16)
          .fontColor('#666666')
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
      .height('100%')
      .scrollBar(BarState.Off)
      .edgeEffect(EdgeEffect.Spring)
      .align(Alignment.Top)
      .expandSafeArea([SafeAreaType.SYSTEM], [SafeAreaEdge.BOTTOM]);
    }
    .title(buildTitleBar(this.vm.title, this.vm.formPage === CommonConstants.CLASSIFICATION_PAGE),
      { paddingStart: LengthMetrics.vp(16) });
  }
}


